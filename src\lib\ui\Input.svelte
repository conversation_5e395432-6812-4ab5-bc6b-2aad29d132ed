<script lang="ts">
import type { Snippet } from 'svelte';
	import { P1 } from '.';

interface Props {
    label?: string;
    value?: any;
    placeholder?: string;
    type?: string;
    error?: string;
    fullWidth?: boolean;
    disabled?: boolean;
    name?: string;
    id?: string;
    required?: boolean;
    onInput?: (e: Event) => void;
    onBlur?: (e: Event) => void;
    onFocus?: (e: Event) => void;
    onkeydown?: (e: KeyboardEvent) => void;
    children?: Snippet;
}

let {
    label = '',
    value = $bindable(''),
    placeholder = '',
    type = 'text',
    error = '',
    fullWidth = false,
    disabled = false,
    name = '',
    id = '',
    required = false,
    onInput = undefined,
    onBlur = undefined,
    onFocus = undefined,
    onkeydown = undefined,
    children
}: Props = $props();

let inputElement: HTMLInputElement;

export function focus() {
    inputElement?.focus();
}
</script>

<div class="input-root" class:fullWidth>
    {#if label}
        <P1>{label}</P1>
    {/if}
    <div class="input-container" class:fullWidth>
        <input
            bind:this={inputElement}
            bind:value
            class="input-field"
            {type}
            {placeholder}
            {name}
            {id}
            {disabled}
            {required}
            oninput={onInput}
            onblur={onBlur}
            onfocus={onFocus}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={error ? `${id}-error` : undefined}
            autocomplete="off"
            onkeydown={onkeydown}
        />
        {#if error}
            <div class="input-error" id={id + '-error'}>{error}</div>
        {/if}
        {@render children?.()}
    </div>
</div>

<style>
    .input-root {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        align-items: start;
        gap: 0.5rem;
        width: var(--width, fit-content);
        font-family: 'Open Sans', sans-serif;
        height: var(--height, fit-content);
    }
    
    .fullWidth {
        width: 100%;
    }

    .input-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 0.5rem;
    }

    .input-field {
        flex: 1 1 auto;
        padding: 0.75rem 1.25rem;
        font-size: 1.125rem;
        border: 2.5px solid var(--pitch-black);
        border-radius: 0.5rem;
        background: var(--white);
        color: var(--pitch-black);
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        outline: none;
        font-weight: 600;
        transition: box-shadow 0.1s, transform 0.1s;
    }

    .input-field:focus {
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
        transform: translate(-0.125rem, -0.125rem);
    }

    .input-field:disabled {
        background: #EEEEEE;
        color: #888888;
        cursor: not-allowed;
    }

    .input-error {
        color: var(--rose);
        font-size: 0.95rem;
        font-weight: 500;
        margin-top: 0.1rem;
        background: var(--light-rose);
        border: 1.5px solid var(--rose);
        border-radius: 0.25rem;
        padding: 0.25rem 0.5rem;
        box-shadow: 0.15rem 0.15rem 0 var(--rose);
        width: fit-content;
    }

    @media (max-width: 540px) {
        .input-container {
            flex-direction: column;
            gap: 2rem;
        }

        .input-field {
            width: 100%;
        }

    }
</style> 